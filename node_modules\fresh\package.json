{"_from": "fresh@0.5.2", "_id": "fresh@0.5.2", "_inBundle": false, "_integrity": "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==", "_location": "/fresh", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "fresh@0.5.2", "name": "fresh", "escapedName": "fresh", "rawSpec": "0.5.2", "saveSpec": null, "fetchSpec": "0.5.2"}, "_requiredBy": ["/express", "/send"], "_resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "_shasum": "3d8cadd90d976569fa835ab1f8e4b23a105605a7", "_spec": "fresh@0.5.2", "_where": "D:\\13toolwin.github.io\\node_modules\\express", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "deprecated": false, "description": "HTTP response freshness testing", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-node": "5.1.1", "eslint-plugin-promise": "3.5.0", "eslint-plugin-standard": "3.0.1", "istanbul": "0.4.5", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "homepage": "https://github.com/jshttp/fresh#readme", "keywords": ["fresh", "http", "conditional", "cache"], "license": "MIT", "name": "fresh", "repository": {"type": "git", "url": "git+https://github.com/jshttp/fresh.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "0.5.2"}
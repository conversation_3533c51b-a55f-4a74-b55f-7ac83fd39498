{"_from": "process-nextick-args@~2.0.0", "_id": "process-nextick-args@2.0.1", "_inBundle": false, "_integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "_location": "/process-nextick-args", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "process-nextick-args@~2.0.0", "name": "process-nextick-args", "escapedName": "process-nextick-args", "rawSpec": "~2.0.0", "saveSpec": null, "fetchSpec": "~2.0.0"}, "_requiredBy": ["/readable-stream"], "_resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "_shasum": "7820d9b16120cc55ca9ae7792680ae7dba6d7fe2", "_spec": "process-nextick-args@~2.0.0", "_where": "D:\\13toolwin.github.io\\node_modules\\readable-stream", "author": "", "bugs": {"url": "https://github.com/calvinmetcalf/process-nextick-args/issues"}, "bundleDependencies": false, "deprecated": false, "description": "process.nextTick but always with args", "devDependencies": {"tap": "~0.2.6"}, "files": ["index.js"], "homepage": "https://github.com/calvinmetcalf/process-nextick-args", "license": "MIT", "main": "index.js", "name": "process-nextick-args", "repository": {"type": "git", "url": "git+https://github.com/calvinmetcalf/process-nextick-args.git"}, "scripts": {"test": "node test.js"}, "version": "2.0.1"}
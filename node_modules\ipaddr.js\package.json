{"_from": "ipaddr.js@1.9.1", "_id": "ipaddr.js@1.9.1", "_inBundle": false, "_integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==", "_location": "/ipaddr.js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ipaddr.js@1.9.1", "name": "ipaddr.js", "escapedName": "ipaddr.js", "rawSpec": "1.9.1", "saveSpec": null, "fetchSpec": "1.9.1"}, "_requiredBy": ["/proxy-addr"], "_resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "_shasum": "bff38543eeb8984825079ff3a2a8e6cbd46781b3", "_spec": "ipaddr.js@1.9.1", "_where": "D:\\13toolwin.github.io\\node_modules\\proxy-addr", "author": {"name": "whitequark", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/whitequark/ipaddr.js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "devDependencies": {"coffee-script": "~1.12.6", "nodeunit": "^0.11.3", "uglify-js": "~3.0.19"}, "directories": {"lib": "./lib"}, "engines": {"node": ">= 0.10"}, "files": ["lib/", "LICENSE", "ipaddr.min.js"], "homepage": "https://github.com/whitequark/ipaddr.js#readme", "keywords": ["ip", "ipv4", "ipv6"], "license": "MIT", "main": "./lib/ipaddr.js", "name": "ipaddr.js", "repository": {"type": "git", "url": "git://github.com/whitequark/ipaddr.js.git"}, "scripts": {"test": "cake build test"}, "types": "./lib/ipaddr.js.d.ts", "version": "1.9.1"}
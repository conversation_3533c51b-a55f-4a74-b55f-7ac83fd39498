<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qu<PERSON>n lý tài khoản ngân hàng</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .tab-button {
            transition: all 0.3s ease;
        }
        .tab-button.active {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .upload-area {
            transition: all 0.3s ease;
        }
        .upload-area.dragover {
            background-color: #dbeafe;
            border-color: #3b82f6;
        }
        .password-hidden {
            font-family: monospace;
            letter-spacing: 2px;
        }
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-university mr-3 text-blue-600"></i>
                    Quản lý tài khoản ngân hàng
                </h1>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- File Upload Section -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">
                <i class="fas fa-upload mr-2 text-green-600"></i>
                Upload File Tài Khoản
            </h2>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Chọn Database/Tab để upload:
                </label>
                <select id="targetTab" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="tab1">Tab 1 - Database 1</option>
                    <option value="tab2">Tab 2 - Database 2</option>
                    <option value="tab3">Tab 3 - Database 3</option>
                    <option value="tab4">Tab 4 - Database 4</option>
                </select>
            </div>

            <div id="uploadArea" class="upload-area relative border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 cursor-pointer">
                <input type="file" id="fileInput" accept=".txt" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer">

                <div id="uploadContent" class="space-y-2">
                    <i class="fas fa-file-text text-4xl text-gray-400 mb-4"></i>
                    <div class="text-gray-600">
                        <span class="font-medium text-blue-600 hover:text-blue-500">
                            Click để chọn file
                        </span>
                        hoặc kéo thả file vào đây
                    </div>
                    <p class="text-sm text-gray-500">
                        Chỉ hỗ trợ file .txt (định dạng accounts_*.txt)
                    </p>
                </div>

                <div id="uploadLoading" class="hidden space-y-2">
                    <i class="fas fa-spinner loading text-2xl text-blue-600"></i>
                    <div class="text-blue-600">Đang upload...</div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button class="tab-button active px-4 py-2 font-medium text-sm rounded-lg transition-colors bg-blue-500 text-white" data-tab="tab1">
                        Database 1
                        <span class="ml-2 bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs" id="count-tab1">0</span>
                    </button>
                    <button class="tab-button px-4 py-2 font-medium text-sm rounded-lg transition-colors text-green-600 hover:bg-green-50" data-tab="tab2">
                        Database 2
                        <span class="ml-2 bg-green-100 px-2 py-1 rounded-full text-xs text-green-800" id="count-tab2">0</span>
                    </button>
                    <button class="tab-button px-4 py-2 font-medium text-sm rounded-lg transition-colors text-purple-600 hover:bg-purple-50" data-tab="tab3">
                        Database 3
                        <span class="ml-2 bg-purple-100 px-2 py-1 rounded-full text-xs text-purple-800" id="count-tab3">0</span>
                    </button>
                    <button class="tab-button px-4 py-2 font-medium text-sm rounded-lg transition-colors text-orange-600 hover:bg-orange-50" data-tab="tab4">
                        Database 4
                        <span class="ml-2 bg-orange-100 px-2 py-1 rounded-full text-xs text-orange-800" id="count-tab4">0</span>
                    </button>
                </nav>
            </div>
        </div>

        <!-- Content -->
        <div class="space-y-6">
            <div class="flex justify-between items-center">
                <h2 id="tabTitle" class="text-2xl font-bold text-gray-900">
                    Database 1 - Danh sách tài khoản
                </h2>
                <div class="text-sm text-gray-500">
                    Tổng: <span id="totalCount">0</span> tài khoản
                </div>
            </div>

            <!-- Loading -->
            <div id="loading" class="hidden flex justify-center items-center py-12">
                <i class="fas fa-spinner loading text-2xl text-blue-600 mr-2"></i>
                <span>Đang tải...</span>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="hidden bg-white rounded-lg shadow-md p-8 text-center">
                <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-500 text-lg">Chưa có tài khoản nào</p>
                <p class="text-gray-400 text-sm mt-2">Upload file để thêm tài khoản</p>
            </div>

            <!-- Accounts Table -->
            <div id="accountsTable" class="hidden bg-white rounded-lg shadow-md overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STT</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tài khoản</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mật khẩu</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Họ tên</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái TK</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số dư</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thưởng</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngân hàng</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="accountsBody" class="bg-white divide-y divide-gray-200">
                            <!-- Accounts will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <script src="script.js"></script>
</body>
</html>

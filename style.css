* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
}

header h1 i {
    margin-right: 15px;
    color: #3498db;
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.file-info {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.info-item i {
    color: #3498db;
}

.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    align-items: center;
}

.stat-item {
    background: #ecf0f1;
    padding: 10px 15px;
    border-radius: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
    min-width: 120px;
}

.stat-item.available {
    background: rgba(46, 204, 113, 0.2);
    color: #27ae60;
}

.stat-item.in-use {
    background: rgba(241, 196, 15, 0.2);
    color: #f39c12;
}

.stat-item.used-up {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.stat-label {
    font-size: 0.9rem;
}

.stat-item span:last-child {
    font-weight: 700;
    font-size: 1.1rem;
}

.upload-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.button-row {
    display: flex;
    gap: 10px;
    align-items: center;
}

.file-input-wrapper {
    position: relative;
    overflow: hidden;
    display: inline-block;
}

.file-input-wrapper input[type=file] {
    position: absolute;
    left: -9999px;
}

.btn-upload {
    background: #27ae60;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-upload:hover {
    background: #229954;
    transform: translateY(-1px);
}

.btn-refresh {
    background: #f39c12;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-refresh:hover {
    background: #e67e22;
    transform: translateY(-1px);
}

.btn-clear {
    background: #e74c3c;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-clear:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.url-input-wrapper {
    display: flex;
    gap: 10px;
    align-items: center;
}

#urlInput {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

#urlInput:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

#urlInput::placeholder {
    color: #adb5bd;
    font-style: italic;
}

.btn-load-url {
    background: #9b59b6;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.btn-load-url:hover {
    background: #8e44ad;
    transform: translateY(-1px);
}

.file-name {
    font-size: 0.85rem;
    color: #6c757d;
    font-style: italic;
}

.table-container {
    overflow-x: auto;
    max-height: 70vh;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.95rem;
}

thead {
    background: #34495e;
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

th, td {
    padding: 15px 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

tbody tr {
    transition: all 0.3s ease;
}

tbody tr:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.account-info {
    font-weight: 600;
    color: #2c3e50;
}

.password-field {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 5px 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.note-field {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #fff;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.note-field:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.note-field::placeholder {
    color: #adb5bd;
    font-style: italic;
}

.status-select {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #fff;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
    cursor: pointer;
}

.status-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Màu nền cho các hàng theo trạng thái */
.account-row.available {
    background-color: rgba(46, 204, 113, 0.1);
}

.account-row.in-use {
    background-color: rgba(241, 196, 15, 0.1);
}

.account-row.used-up {
    background-color: rgba(231, 76, 60, 0.1);
}

.account-row.available:hover {
    background-color: rgba(46, 204, 113, 0.2);
}

.account-row.in-use:hover {
    background-color: rgba(241, 196, 15, 0.2);
}

.account-row.used-up:hover {
    background-color: rgba(231, 76, 60, 0.2);
}

.status-success {
    background: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 600;
}

.reward {
    background: #fff3cd;
    color: #856404;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 600;
}

.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin: 2px;
}

.btn-copy {
    background: #3498db;
    color: white;
}

.btn-copy:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-detail {
    background: #95a5a6;
    color: white;
}

.btn-detail:hover {
    background: #7f8c8d;
    transform: translateY(-1px);
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.loading, .no-data {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.loading i, .no-data i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #3498db;
}

.loading p, .no-data p {
    font-size: 1.2rem;
}

.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #27ae60;
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
}

.toast i {
    margin-right: 10px;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: #34495e;
    color: white;
    padding: 20px 25px;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-weight: 300;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #3498db;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.detail-item {
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.detail-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.detail-value {
    font-family: 'Courier New', monospace;
    background: white;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 10px;
    }

    header h1 {
        font-size: 1.8rem;
    }

    .controls {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .search-box {
        max-width: none;
    }

    table {
        font-size: 0.85rem;
    }

    th, td {
        padding: 10px 8px;
    }

    .btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}

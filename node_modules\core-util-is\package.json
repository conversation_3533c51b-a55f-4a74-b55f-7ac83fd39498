{"_from": "core-util-is@~1.0.0", "_id": "core-util-is@1.0.3", "_inBundle": false, "_integrity": "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==", "_location": "/core-util-is", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "core-util-is@~1.0.0", "name": "core-util-is", "escapedName": "core-util-is", "rawSpec": "~1.0.0", "saveSpec": null, "fetchSpec": "~1.0.0"}, "_requiredBy": ["/readable-stream"], "_resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "_shasum": "a6042d3634c2b27e9328f837b965fac83808db85", "_spec": "core-util-is@~1.0.0", "_where": "D:\\13toolwin.github.io\\node_modules\\readable-stream", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The `util.is*` functions introduced in Node v0.12.", "devDependencies": {"tap": "^15.0.9"}, "files": ["lib"], "homepage": "https://github.com/isaacs/core-util-is#readme", "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "license": "MIT", "main": "lib/util.js", "name": "core-util-is", "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is.git"}, "scripts": {"postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preversion": "npm test", "test": "tap test.js"}, "version": "1.0.3"}
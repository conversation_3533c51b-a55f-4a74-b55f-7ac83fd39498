{"_from": "m<PERSON>@^1.4.5-lts.1", "_id": "multer@1.4.5-lts.2", "_inBundle": false, "_integrity": "sha512-VzGiVigcG9zUAoCNU+xShztrlr1auZOlurXynNvO9GiWD1/mTBbUljOKY+qMeazBqXgRnjzeEgJI/wyjJUHg9A==", "_location": "/multer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "m<PERSON>@^1.4.5-lts.1", "name": "multer", "escapedName": "multer", "rawSpec": "^1.4.5-lts.1", "saveSpec": null, "fetchSpec": "^1.4.5-lts.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/multer/-/multer-1.4.5-lts.2.tgz", "_shasum": "340af065d8685dda846ec9e3d7655fcd50afba2d", "_spec": "m<PERSON>@^1.4.5-lts.1", "_where": "D:\\13toolwin.github.io", "bugs": {"url": "https://github.com/expressjs/multer/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.hacksparrow.com"}, {"name": "<PERSON><PERSON>", "email": "https://github.com/jpfluger"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"append-field": "^1.0.0", "busboy": "^1.0.0", "concat-stream": "^1.5.2", "mkdirp": "^0.5.4", "object-assign": "^4.1.1", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "deprecated": "Multer 1.x is impacted by a number of vulnerabilities, which have been patched in 2.x. You should upgrade to the latest 2.x version.", "description": "Middleware for handling `multipart/form-data`.", "devDependencies": {"deep-equal": "^2.0.3", "express": "^4.13.1", "form-data": "^1.0.0-rc1", "fs-temp": "^1.1.2", "mocha": "^3.5.3", "rimraf": "^2.4.1", "standard": "^14.3.3", "testdata-w3c-json-form": "^1.0.0"}, "engines": {"node": ">= 6.0.0"}, "files": ["LICENSE", "index.js", "storage/", "lib/"], "homepage": "https://github.com/expressjs/multer#readme", "keywords": ["form", "post", "multipart", "form-data", "formdata", "express", "middleware"], "license": "MIT", "name": "multer", "repository": {"type": "git", "url": "git+https://github.com/expressjs/multer.git"}, "scripts": {"test": "standard && mocha"}, "version": "1.4.5-lts.2"}
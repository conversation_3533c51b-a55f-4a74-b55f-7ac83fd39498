{"_from": "unpipe@1.0.0", "_id": "unpipe@1.0.0", "_inBundle": false, "_integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==", "_location": "/unpipe", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "unpipe@1.0.0", "name": "unpipe", "escapedName": "unpipe", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/body-parser", "/finalhandler", "/raw-body"], "_resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "_shasum": "b2bf4ee8514aae6165b4817829d21b2ef49904ec", "_spec": "unpipe@1.0.0", "_where": "D:\\13toolwin.github.io\\node_modules\\body-parser", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/stream-utils/unpipe/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Unpipe a stream from all destinations", "devDependencies": {"istanbul": "0.3.15", "mocha": "2.2.5", "readable-stream": "1.1.13"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/stream-utils/unpipe#readme", "license": "MIT", "name": "unpipe", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/unpipe.git"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "1.0.0"}
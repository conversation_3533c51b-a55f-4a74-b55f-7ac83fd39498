{"_from": "ee-first@1.1.1", "_id": "ee-first@1.1.1", "_inBundle": false, "_integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==", "_location": "/ee-first", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ee-first@1.1.1", "name": "ee-first", "escapedName": "ee-first", "rawSpec": "1.1.1", "saveSpec": null, "fetchSpec": "1.1.1"}, "_requiredBy": ["/on-finished"], "_resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "_shasum": "590c61156b0ae2f4f0255732a158b266bc56b21d", "_spec": "ee-first@1.1.1", "_where": "D:\\13toolwin.github.io\\node_modules\\on-finished", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "return the first event in a set of ee/event pairs", "devDependencies": {"istanbul": "0.3.9", "mocha": "2.2.5"}, "files": ["index.js", "LICENSE"], "homepage": "https://github.com/jonathanong/ee-first#readme", "license": "MIT", "name": "ee-first", "repository": {"type": "git", "url": "git+https://github.com/jonathanong/ee-first.git"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "1.1.1"}
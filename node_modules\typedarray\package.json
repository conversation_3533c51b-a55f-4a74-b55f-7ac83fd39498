{"_from": "typedarray@^0.0.6", "_id": "typedarray@0.0.6", "_inBundle": false, "_integrity": "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==", "_location": "/typedarray", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "typedarray@^0.0.6", "name": "typedarray", "escapedName": "typedarray", "rawSpec": "^0.0.6", "saveSpec": null, "fetchSpec": "^0.0.6"}, "_requiredBy": ["/concat-stream"], "_resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "_shasum": "867ac74e3864187b1d3d47d996a78ec5c8830777", "_spec": "typedarray@^0.0.6", "_where": "D:\\13toolwin.github.io\\node_modules\\concat-stream", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/typedarray/issues"}, "bundleDependencies": false, "deprecated": false, "description": "TypedArray polyfill for old browsers", "devDependencies": {"tape": "~2.3.2"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "license": "MIT", "main": "index.js", "name": "typedarray", "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "0.0.6"}
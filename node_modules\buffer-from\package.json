{"_from": "buffer-from@^1.0.0", "_id": "buffer-from@1.1.2", "_inBundle": false, "_integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "_location": "/buffer-from", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "buffer-from@^1.0.0", "name": "buffer-from", "escapedName": "buffer-from", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/concat-stream"], "_resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "_shasum": "2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5", "_spec": "buffer-from@^1.0.0", "_where": "D:\\13toolwin.github.io\\node_modules\\concat-stream", "bugs": {"url": "https://github.com/LinusU/buffer-from/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A [ponyfill](https://ponyfill.com) for `Buffer.from`, uses native implementation if available.", "devDependencies": {"standard": "^12.0.1"}, "files": ["index.js"], "homepage": "https://github.com/LinusU/buffer-from#readme", "keywords": ["buffer", "buffer from"], "license": "MIT", "name": "buffer-from", "repository": {"type": "git", "url": "git+https://github.com/LinusU/buffer-from.git"}, "scripts": {"test": "standard && node test"}, "version": "1.1.2"}
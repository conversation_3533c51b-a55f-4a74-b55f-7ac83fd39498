{"_from": "serve-static@1.16.2", "_id": "serve-static@1.16.2", "_inBundle": false, "_integrity": "sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==", "_location": "/serve-static", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "serve-static@1.16.2", "name": "serve-static", "escapedName": "serve-static", "rawSpec": "1.16.2", "saveSpec": null, "fetchSpec": "1.16.2"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz", "_shasum": "b6a5343da47f6bdd2673848bf45754941e803296", "_spec": "serve-static@1.16.2", "_where": "D:\\13toolwin.github.io\\node_modules\\express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "bundleDependencies": false, "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "deprecated": false, "description": "Serve static files", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0", "safe-buffer": "5.2.1", "supertest": "6.2.2"}, "engines": {"node": ">= 0.8.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/expressjs/serve-static#readme", "license": "MIT", "name": "serve-static", "repository": {"type": "git", "url": "git+https://github.com/expressjs/serve-static.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "version": "1.16.2"}